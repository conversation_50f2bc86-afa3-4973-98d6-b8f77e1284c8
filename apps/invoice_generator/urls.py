from django.urls import path
from .views import (
    extract_template_information,
    InvoiceTemplateListView,
    InvoiceTemplateDetailView,
    CompanyTemplateListCreateView,
    CompanyTemplateDetailView,
    CompanyLogoUploadView,
    SalesWorkflowListCreateView,
    SalesWorkflowDetailView,
    CSVUploadView,
    ColumnMappingSuggestionsView,
    StartWorkflowView,
    GenerateInvoicesView,
    DownloadInvoiceView,
    DownloadInvoicesZipView,
)

urlpatterns = [
    # Legacy endpoints
    path(
        "extract-template-information/",
        extract_template_information,
        name="extract_template_information",
    ),
    path(
        "templates/",
        InvoiceTemplateListView.as_view(),
        name="invoice_templates_list",
    ),
    path(
        "templates/<str:template_id>/",
        InvoiceTemplateDetailView.as_view(),
        name="invoice_template_detail",
    ),
    # Company Template endpoints
    path(
        "company-templates/",
        CompanyTemplateListCreateView.as_view(),
        name="company_templates_list_create",
    ),
    path(
        "company-templates/<int:pk>/",
        CompanyTemplateDetailView.as_view(),
        name="company_template_detail",
    ),
    path(
        "company-templates/upload-logo/",
        CompanyLogoUploadView.as_view(),
        name="company_logo_upload",
    ),
    # Sales Workflow endpoints
    path(
        "workflows/",
        SalesWorkflowListCreateView.as_view(),
        name="sales_workflows_list_create",
    ),
    path(
        "workflows/<int:pk>/",
        SalesWorkflowDetailView.as_view(),
        name="sales_workflow_detail",
    ),
    path(
        "workflows/start/",
        StartWorkflowView.as_view(),
        name="start_workflow",
    ),
    # CSV Processing endpoints
    path(
        "csv-upload/",
        CSVUploadView.as_view(),
        name="csv_upload",
    ),
    path(
        "column-mapping-suggestions/",
        ColumnMappingSuggestionsView.as_view(),
        name="column_mapping_suggestions",
    ),
    # Invoice Generation endpoints
    path(
        "generate-invoices/",
        GenerateInvoicesView.as_view(),
        name="generate_invoices",
    ),
    # Test endpoint
    path(
        "test-generate/",
        GenerateInvoicesView.as_view(),
        name="test_generate",
    ),
    path(
        "download-invoice/<path:file_path>/",
        DownloadInvoiceView.as_view(),
        name="download_invoice",
    ),
    path(
        "download-invoices-zip/<str:user_folder>/",
        DownloadInvoicesZipView.as_view(),
        name="download_invoices_zip",
    ),
]
